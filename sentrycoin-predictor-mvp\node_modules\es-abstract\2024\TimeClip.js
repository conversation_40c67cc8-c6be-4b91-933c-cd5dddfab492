'use strict';

var GetIntrinsic = require('get-intrinsic');

var $Date = GetIntrinsic('%Date%');

var $isFinite = require('math-intrinsics/isFinite');
var abs = require('math-intrinsics/abs');

var ToNumber = require('./ToNumber');

// https://262.ecma-international.org/5.1/#sec-*********

module.exports = function TimeClip(time) {
	if (!$isFinite(time) || abs(time) > 8.64e15) {
		return NaN;
	}
	return +new $Date(ToNumber(time));
};

