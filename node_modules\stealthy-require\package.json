{"name": "stealthy-require", "version": "1.1.1", "description": "The closest you can get to require something with bypassing the require cache", "keywords": ["require", "bypass", "no", "cache", "fresh"], "main": "./lib/index.js", "scripts": {"test": "./node_modules/.bin/gulp ci", "test-publish": "./node_modules/.bin/gulp ci-no-cov", "publish-please": "publish-please", "prepublish": "publish-please guard"}, "repository": {"type": "git", "url": "git+https://github.com/analog-nico/stealthy-require.git"}, "author": "<PERSON><PERSON> (https://github.com/analog-nico)", "license": "ISC", "bugs": {"url": "https://github.com/analog-nico/stealthy-require/issues"}, "homepage": "https://github.com/analog-nico/stealthy-require#readme", "engines": {"node": ">=0.10.0"}, "dependencies": {}, "devDependencies": {"bcrypt": "~1.0.2", "browserify": "~14.3.0", "chai": "~3.5.0", "chalk": "~1.1.3", "gulp": "~3.9.1", "gulp-coveralls": "~0.1.4", "gulp-eslint": "~2.1.0", "gulp-istanbul": "~1.1.1", "gulp-mocha": "~3.0.1", "mkdirp": "~0.5.1", "publish-please": "~2.3.1", "rimraf": "~2.6.1", "run-sequence": "~1.2.2", "webpack": "~1.15.0"}}