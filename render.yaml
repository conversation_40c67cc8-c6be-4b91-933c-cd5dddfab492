services:
  - type: web
    name: sentrycoin-flash-crash-predictor
    runtime: node
    plan: free
    buildCommand: "npm install"
    startCommand: "node src/index.js"
    envVars:
      - key: TELEGRAM_BOT_TOKEN
        sync: false
      - key: TELEGRAM_CHAT_ID
        sync: false
      - key: TELEGRAM_API_ID
        sync: false
      - key: TELEGRAM_API_HASH
        sync: false
      - key: SYMBOL
        value: SOLUSDT
      - key: EXCHANGE
        value: binance
      - key: NODE_ENV
        value: production
      - key: DANGER_RATIO
        value: "2.5"
      - key: ORDER_BOOK_DEPTH
        value: "50"
      - key: COOLDOWN_MINUTES
        value: "5"
      - key: LOG_LEVEL
        value: info
      - key: NODE_ENV
        value: production
